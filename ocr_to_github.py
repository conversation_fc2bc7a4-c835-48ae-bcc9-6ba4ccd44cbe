#!/usr/bin/env python3
"""
ForceGuides OCR Price Updater
Transforms OCR slot data to item prices and uploads to GitHub

SETUP INSTRUCTIONS:
1. Install required packages: pip install requests
2. Create GitHub Personal Access Token with 'repo' permissions
3. Update the configuration variables below
4. Update SLOT_MAPPING to match your js/slot-mapping.js file
5. Run your OCR script to generate slot data
6. Call transform_and_upload() with your OCR data

Example usage:
    ocr_data = your_ocr_function()  # Your existing OCR data
    success = transform_and_upload(ocr_data)
"""

import requests
import json
import base64
from datetime import datetime
import os

# CONFIGURATION - UPDATE THESE VALUES:
GITHUB_TOKEN = "your_github_token_here"  # Replace with your GitHub token
GITHUB_OWNER = "your_username"           # Replace with your GitHub username
GITHUB_REPO = "forceguides-new-calculators"  # Your repo name
GITHUB_BRANCH = "main"                   # Your default branch (main or master)

# SLOT MAPPING - UPDATE TO MATCH YOUR js/slot-mapping.js FILE:
SLOT_MAPPING = {
    1: None,   # Replace with: "Upgrade Core(Low)" or whatever you have in slot 1
    2: None,   # Replace with: "Upgrade Core(Medium)" or whatever you have in slot 2
    3: None,   # Replace with: "Upgrade Core(High)" or whatever you have in slot 3
    4: None,   # Replace with: "Upgrade Core(Highest)" or whatever you have in slot 4
    5: None,   # Replace with: "Force Core(Low)" or whatever you have in slot 5
    6: None,   # Replace with: "Force Core(Medium)" or whatever you have in slot 6
    7: None,   # Replace with: "Force Core(High)" or whatever you have in slot 7
    8: None,   # Replace with: "Force Core(Highest)" or whatever you have in slot 8
    9: None,   # Replace with: "Material Core(Mithril)" or whatever you have in slot 9
    10: None,  # Replace with: "Material Core(Archridium)" or whatever you have in slot 10
    11: None,  # Replace with: "Slot Extender(High)" or whatever you have in slot 11
    12: None   # Replace with: "Slot Extender(Highest)" or whatever you have in slot 12
}

def transform_ocr_to_prices(ocr_data):
    """
    Convert OCR slot data to item prices
    
    Args:
        ocr_data: Dictionary with slot data from your OCR script
        
    Returns:
        Dictionary with transformed price data
    """
    prices = {}
    timestamp = None
    
    print(f"Processing {len(ocr_data)} slots...")
    
    for slot_key, slot_data in ocr_data.items():
        slot_number = slot_data.get('slot_number')
        min_price = slot_data.get('min_price')
        
        # Get item name from slot mapping
        item_name = SLOT_MAPPING.get(slot_number)
        
        if item_name and min_price:
            try:
                # Convert price to integer (remove any commas/formatting)
                price_value = int(str(min_price).replace(',', '').replace('.', ''))
                prices[item_name] = price_value
                print(f"  Slot {slot_number}: {item_name} = {price_value:,} ALZ")
            except (ValueError, TypeError):
                print(f"  Slot {slot_number}: Invalid price '{min_price}' for {item_name}")
        elif item_name:
            print(f"  Slot {slot_number}: {item_name} - No price data")
        elif slot_number:
            print(f"  Slot {slot_number}: No item mapping configured")
            
        # Use the most recent timestamp
        slot_timestamp = slot_data.get('timestamp')
        if slot_timestamp and (not timestamp or slot_timestamp > timestamp):
            timestamp = slot_timestamp
    
    print(f"Successfully mapped {len(prices)} items")
    
    return {
        'timestamp': timestamp or datetime.now().isoformat(),
        'prices': prices,
        'raw_ocr_data': ocr_data  # Keep original for debugging
    }

def upload_to_github(price_data):
    """
    Upload price data to GitHub repository
    
    Args:
        price_data: Dictionary with price data from transform_ocr_to_prices()
        
    Returns:
        Boolean indicating success
    """
    if not GITHUB_TOKEN or GITHUB_TOKEN == "your_github_token_here":
        print("ERROR: GitHub token not configured!")
        return False
        
    if not any(SLOT_MAPPING.values()):
        print("ERROR: No slot mappings configured!")
        return False
    
    # GitHub API endpoint
    file_path = "data/prices.json"
    url = f"https://api.github.com/repos/{GITHUB_OWNER}/{GITHUB_REPO}/contents/{file_path}"
    
    # Prepare the new content
    content_json = json.dumps(price_data, indent=2)
    content_encoded = base64.b64encode(content_json.encode()).decode()
    
    headers = {
        'Authorization': f'token {GITHUB_TOKEN}',
        'Accept': 'application/vnd.github.v3+json'
    }
    
    try:
        # Get current file SHA (required for updates)
        print("Checking for existing file...")
        get_response = requests.get(url, headers=headers)
        
        current_sha = None
        if get_response.status_code == 200:
            current_sha = get_response.json()['sha']
            print("Found existing file, will update")
        elif get_response.status_code == 404:
            print("File doesn't exist, will create new")
        else:
            print(f"Error checking file: {get_response.status_code}")
            return False
        
        # Prepare commit payload
        timestamp_str = datetime.now().strftime("%Y-%m-%d %H:%M")
        payload = {
            'message': f'Update market prices - {timestamp_str}',
            'content': content_encoded,
            'branch': GITHUB_BRANCH
        }
        
        if current_sha:
            payload['sha'] = current_sha
        
        # Upload the file
        print("Uploading to GitHub...")
        response = requests.put(url, json=payload, headers=headers)
        
        if response.status_code in [200, 201]:
            print("✅ Successfully uploaded prices to GitHub!")
            print(f"   Updated {len(price_data['prices'])} item prices")
            print(f"   Timestamp: {price_data['timestamp']}")
            return True
        else:
            print(f"❌ Failed to upload: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error uploading to GitHub: {e}")
        return False

def transform_and_upload(ocr_data):
    """
    Complete workflow: transform OCR data and upload to GitHub
    
    Args:
        ocr_data: Dictionary with slot data from your OCR script
        
    Returns:
        Boolean indicating success
    """
    print("=== ForceGuides OCR Price Updater ===")
    
    # Transform OCR data to prices
    price_data = transform_ocr_to_prices(ocr_data)
    
    if not price_data['prices']:
        print("❌ No prices extracted from OCR data")
        return False
    
    # Upload to GitHub
    return upload_to_github(price_data)

def validate_configuration():
    """Validate that configuration is properly set up"""
    issues = []
    
    if not GITHUB_TOKEN or GITHUB_TOKEN == "your_github_token_here":
        issues.append("GitHub token not configured")
    
    if not GITHUB_OWNER or GITHUB_OWNER == "your_username":
        issues.append("GitHub owner/username not configured")
        
    if not any(SLOT_MAPPING.values()):
        issues.append("No slot mappings configured")
        
    mapped_slots = [k for k, v in SLOT_MAPPING.items() if v is not None]
    if len(mapped_slots) < 3:
        issues.append(f"Only {len(mapped_slots)} slots mapped (recommend at least 3)")
    
    if issues:
        print("❌ Configuration Issues:")
        for issue in issues:
            print(f"   - {issue}")
        return False
    
    print("✅ Configuration looks good!")
    print(f"   Mapped {len(mapped_slots)} slots")
    return True

# Example usage and testing
if __name__ == "__main__":
    print("=== Configuration Check ===")
    if not validate_configuration():
        print("\nPlease fix configuration issues before running.")
        exit(1)
    
    print("\n=== Example Usage ===")
    print("# In your OCR script, call:")
    print("# success = transform_and_upload(your_ocr_data)")
    print("\n# Or test with sample data:")
    print("# sample_data = {...}  # Your OCR data structure")
    print("# transform_and_upload(sample_data)")
