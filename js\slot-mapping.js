/**
 * ForceGuides OCR Slot Mapping
 * Maps inventory slot numbers to exact item names used in calculators
 * 
 * INSTRUCTIONS FOR USER:
 * 1. Put items in your inventory slots 1-12 in a consistent order
 * 2. Update the mapping below to match your setup
 * 3. Use EXACT item names as they appear in your calculators
 * 
 * Example item names from your calculators:
 * - "Force Core(Low)", "Force Core(Medium)", "Force Core(High)", "Force Core(Highest)"
 * - "Upgrade Core(Low)", "Upgrade Core(Medium)", "Upgrade Core(High)", "Upgrade Core(Highest)"
 * - "Material Core(Mithril)", "Material Core(Archridium)", "Material Core(Palladium)"
 * - "Slot Extender(High)", "Slot Extender(Highest)"
 * - "Force Core(Piece)", "Force Core(Crystal)"
 * - "Upgrade Core(Piece)", "Upgrade Core(Crystal)"
 */

const SlotMapping = {
    // Map slot numbers to exact item names used in calculators
    // UPDATE THESE TO MATCH YOUR INVENTORY SETUP:
    slots: {
        1: "Force Core(Low)",  // Replace with: "Upgrade Core(Low)" or whatever you have in slot 1
        2: "Force Core(Medium)",  // Replace with: "Upgrade Core(Medium)" or whatever you have in slot 2
        3: "Force Core(High)",  // Replace with: "Upgrade Core(High)" or whatever you have in slot 3
        4: "Force Core(Highest)",  // Replace with: "Upgrade Core(Highest)" or whatever you have in slot 4
        5: "Force Core(Ultimate)",  // Replace with: "Force Core(Low)" or whatever you have in slot 5
        6: "Upgrade Core(Low)",  // Replace with: "Force Core(Medium)" or whatever you have in slot 6
        7: "Upgrade Core(Medium)",  // Replace with: "Force Core(High)" or whatever you have in slot 7
        8: "Upgrade Core(High)",  // Replace with: "Force Core(Highest)" or whatever you have in slot 8
        9: "Upgrade Core(Highest)",  // Replace with: "Material Core(Mithril)" or whatever you have in slot 9
        10: "Upgrade Core(Ultimate)", // Replace with: "Material Core(Archridium)" or whatever you have in slot 10
        11: null, // Replace with: "Slot Extender(High)" or whatever you have in slot 11
        12: null  // Replace with: "Slot Extender(Highest)" or whatever you have in slot 12
    },
    
    // Get item name from slot number
    getItemName: (slotNumber) => {
        return SlotMapping.slots[slotNumber] || null;
    },
    
    // Get slot number from item name
    getSlotNumber: (itemName) => {
        for (const [slot, name] of Object.entries(SlotMapping.slots)) {
            if (name === itemName) {
                return parseInt(slot);
            }
        }
        return null;
    },
    
    // Get all mapped items (for debugging)
    getAllMappedItems: () => {
        const mapped = {};
        for (const [slot, itemName] of Object.entries(SlotMapping.slots)) {
            if (itemName) {
                mapped[slot] = itemName;
            }
        }
        return mapped;
    },
    
    // Validate mapping (check for duplicates)
    validateMapping: () => {
        const items = Object.values(SlotMapping.slots).filter(item => item !== null);
        const uniqueItems = new Set(items);
        
        if (items.length !== uniqueItems.size) {
            console.warn('SlotMapping: Duplicate items detected in mapping!');
            return false;
        }
        
        return true;
    }
};

// Auto-validate on load
if (!SlotMapping.validateMapping()) {
    console.error('SlotMapping: Invalid mapping configuration detected!');
}
