/**
 * Chloe Profit Calculator
 * Calculator for Chloe crafting profit
 */
document.addEventListener('DOMContentLoaded', () => {
    // Initialize ForceCalc.crafting namespace
    ForceCalc.crafting = {
        craftingData: [],
        recipeCategories: [],
        recipesByItemName: {},
        ingredientsByItemName: {},
        currentlyOpenRow: null,

        // Create the main crafting table
        createTable: (data) => {
            // Update headers in createTable function
            const headers = [
                ['⭐', false],
                ['Name', true],
                ['Price Per Piece', false],
                ['Craft Cost per try', false],
                ['Profit Margin per try', true],
                ['Expected Profit per try', true],
                ['Register Cost (Alz)', false, 'register-cost-column'],
                ['Token Cost', false, 'register-cost-column'],
                ['Crafts to Pay Off', false, 'register-cost-column'],
                ['Success Chance', false],
                ['Req. Amity', false]
            ];
            const tableHTML = `
                <div class="table-responsive">
                    <table class="crafting-table">
                        <thead>
                            <tr>
                                ${headers.map(([text, sortable, className]) =>
                                    `<th${sortable ? ' class="sortable"' : ''}${className ? ` class="${className}${sortable ? ' sortable' : ''}"` : ''}>${text}</th>`
                                ).join('')}
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>`;
            const placeholder = document.getElementById('crafting-calculator-placeholder');
            if (placeholder) {
                ForceCalc.crafting.addDemandFilter();
                ForceCalc.crafting.addCategoryFilter();

                // Add the control panel (clear button, OCR button, sales fee, and search bar)
                const controlPanel = document.createElement('div');
                controlPanel.className = 'calculator-controls';
                controlPanel.appendChild(ForceCalc.ui.createClearPricesButton());
                controlPanel.appendChild(ForceCalc.ui.createOCRPricesButton());
                controlPanel.appendChild(ForceCalc.ui.createSalesFeeInput());

                // Add search bar to control panel
                const searchContainer = document.createElement('div');
                searchContainer.id = 'search-container';
                searchContainer.innerHTML = `
                    <span class="search-icon">🔍</span>
                    <input type="text" id="search-input" placeholder="Search recipes...">
                `;
                controlPanel.appendChild(searchContainer);

                placeholder.parentNode.insertBefore(controlPanel, placeholder);

                // Add event listener with debounce for search input
                const searchInput = document.getElementById('search-input');
                searchInput?.addEventListener('input', ForceCalc.utils.debounce(() => {
                    ForceCalc.crafting.filterTable();
                }, 300)); // 300ms debounce delay

                // Add batch calculation modal
                const batchModalHTML = `
                    <div class="batch-modal-overlay" id="batch-modal-overlay">
                        <div class="batch-modal">
                            <div class="batch-modal-header">
                                <h3>Batch Calculation</h3>
                                <button class="batch-modal-close">&times;</button>
                            </div>
                            <div class="batch-modal-content">
                                <div class="batch-input-container">
                                    <label for="batch-quantity">Number of Crafts:</label>
                                    <input type="number" id="batch-quantity" min="1" value="10">
                                </div>
                                <div class="batch-option-container">
                                    <label class="batch-checkbox-label">
                                        <input type="checkbox" id="exclude-register-cost" checked>
                                        Exclude register cost (recipe already unlocked)
                                    </label>
                                    <button class="batch-calculate-btn">Calculate</button>
                                </div>
                                <div class="batch-results" style="display: none;">
                                    <h4>Materials Needed:</h4>
                                    <div class="batch-materials-list"></div>
                                    <div class="batch-summary"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                document.body.insertAdjacentHTML('beforeend', batchModalHTML);

                // Add event listeners for batch modal
                const batchModal = document.getElementById('batch-modal-overlay');
                const closeBtn = batchModal.querySelector('.batch-modal-close');
                const calculateBtn = batchModal.querySelector('.batch-calculate-btn');

                closeBtn.addEventListener('click', () => {
                    batchModal.classList.remove('active');
                });

                calculateBtn.addEventListener('click', () => {
                    ForceCalc.crafting.calculateBatch();
                });

                // Close modal when clicking outside
                batchModal.addEventListener('click', (e) => {
                    if (e.target === batchModal) {
                        batchModal.classList.remove('active');
                    }
                });

                placeholder.insertAdjacentHTML('afterend', tableHTML);

                const table = placeholder.nextElementSibling;
                const tbody = table.querySelector('tbody');

                data.forEach(item => {
                    const [row, detailRow] = ForceCalc.crafting.createTableRow(item);
                    tbody.append(row, detailRow);

                    const itemName = item["Item Name"];

                    if (itemName) {
                        const baseItemName = itemName.replace(/\s*x\s*\d+$/, '');

                        if (!ForceCalc.crafting.recipesByItemName[baseItemName]) {
                            ForceCalc.crafting.recipesByItemName[baseItemName] = [];
                        }
                        ForceCalc.crafting.recipesByItemName[baseItemName].push(item);
                    }

                    for (let i = 1; i <= 5; i++) {
                        const ingredientName = item[`Item${i}`];
                        if (ingredientName) {
                            const normalizedName = ForceCalc.utils.normalizeItemName(ingredientName);
                            if (!ForceCalc.crafting.ingredientsByItemName[normalizedName]) {
                                ForceCalc.crafting.ingredientsByItemName[normalizedName] = [];
                            }
                            ForceCalc.crafting.ingredientsByItemName[normalizedName].push(item);
                        }
                    }
                });

                table.querySelectorAll('th.sortable').forEach(th => th.addEventListener('click', () => ForceCalc.crafting.sortTable(th)));
                table.querySelectorAll('.crafting-item-row').forEach(ForceCalc.crafting.recalculateRow);
                placeholder.remove();
                ForceCalc.crafting.filterTable();

                // Apply initial visibility to register cost columns
                const showRegisterCost = document.getElementById('register-cost-filter')?.checked || false;
                document.querySelectorAll('th.register-cost-column, td.register-cost-column').forEach(col => {
                    col.style.display = showRegisterCost ? 'table-cell' : 'none';
                });
            }
        },

        // Create a table row for a crafting recipe
        createTableRow: (item) => {
            // Use our utility method to get the market price
            const savedMarketPrice = ForceCalc.utils.getMarketPrice(item.Recipe) || '0';
            const craftAmount = parseFloat(item['Item Quantity']) || 1;
            const ingredientsHTML = Array.from({ length: 5 }, (_, i) => ForceCalc.crafting.createIngredientRow(item, i)).join('');

            // Only show info icon for Dice and Roulette items
            const isRNGItem = item.Recipe.includes('Dice') || item.Recipe.includes('Roulette');
            const infoIcon = isRNGItem ?
                `<span class="info-icon" title="Assuming Average yield: ${craftAmount} ${item['Item Name']} per craft">ℹ️</span>` : '';

            // Check if we have an icon for this item
            let recipeDisplay = item.Recipe;

            // Special case for known problematic items - hardcode their icons using a mapping
            const knownProblematicItems = {
                'Shiny Jewel of Nix': 'shiny_jewel_nix_icon.png',
                // Add more items here if needed
            };

            // Check if this is a known problematic item first
            let iconPath = null;
            if (knownProblematicItems[item['Item Name']]) {
                iconPath = knownProblematicItems[item['Item Name']];
            } else if (item.iconPath) {
                // Regular item with iconPath
                iconPath = item.iconPath;
            }

            if (iconPath) {
                // Simple and reliable path detection - check if we're on production
                const isProduction = window.location.hostname.includes('nipperlug') ||
                                   window.location.href.includes('nipperlug') ||
                                   document.location.host.includes('hostinger');

                let fullIconPath;
                if (isProduction) {
                    // Production path (Hostinger)
                    fullIconPath = `/wp-content/plugins/nipperlug-new-calculators/assets/images_craft_icons/${iconPath}`;
                } else {
                    // Local path (XAMPP or other local setup)
                    // Check if URL contains wordpress
                    const hasWordPressInPath = window.location.href.includes('/wordpress/');

                    if (hasWordPressInPath) {
                        fullIconPath = `/wordpress/wp-content/plugins/forceguides-new-calculators/assets/images_craft_icons/${iconPath}`;
                    } else {
                        // The case when running on localhost without /wordpress/ in URL
                        fullIconPath = `/wp-content/plugins/forceguides-new-calculators/assets/images_craft_icons/${iconPath}`;
                    }
                }

                recipeDisplay = `<div class="item-with-icon">
                    <img src="${fullIconPath}" alt="${item['Item Name']}" class="item-icon">
                    <span>${item.Recipe}</span>
                </div>`;
            }

            // Format register cost with dots
            const registerCost = item['Register Cost Alz'] || 0;
            const formattedRegisterCost = registerCost > 0 ? ForceCalc.utils.formatWithDots(registerCost.toString()) : 'N/A';

            // Format token cost
            const tokenCost = item['Tokens'] || 0;
            const formattedTokenCost = tokenCost > 0 ? tokenCost.toString() : 'N/A';

            const rowHTML = `
                <tr class="crafting-item-row"
                    data-category="${item.Category || 'N/A'}"
                    data-item-name="${item['Item Name'] || 'N/A'}"
                    data-recipe-name="${item.Recipe || 'N/A'}"
                    data-success-chance="${item['Success Rate'] || 0}"
                    data-craft-amount="${craftAmount}"
                    data-register-cost="${registerCost}">
                    <td>
                        <span class="favorite-btn ${ForceCalc.data.favoriteRecipes.has(item.Recipe) ? 'active' : ''}" data-recipe="${item.Recipe}">♥</span>
                    </td>
                    <td class="sortable">${recipeDisplay} ${infoIcon}</td>
                    <td><span contenteditable="true" class="market-price" data-item-name="${item.Recipe}">${savedMarketPrice}</span></td>
                    <td class="total-craft-cost">0</td>
                    <td class="profit-margin sortable">0.00%</td>
                    <td class="profit-per-craft sortable">0</td>
                    <td class="register-cost sortable register-cost-column">${formattedRegisterCost}</td>
                    <td class="token-cost sortable register-cost-column">${formattedTokenCost}</td>
                    <td class="crafts-to-pay-off sortable register-cost-column">-</td>
                    <td>${item['Success Rate'] ? item['Success Rate'] + '%' : 'N/A'}</td>
                    <td>${item['Req Amity'] || '0'}</td>
                </tr>
                <tr class="recipe-details-row" style="display:none">
                    <td colspan="11">
                        <div class="ingredient-details-container">
                            <div class="ingredient-caption">Ingredients required to craft <strong>${item.Recipe}</strong></div>
                            <button class="batch-calc-btn" data-recipe="${item.Recipe}">Batch Calculate</button>
                        </div>
                        <table class="ingredient-grid">
                            <thead>
                                <tr><th>Item</th><th>Quantity</th><th>Price per unit</th><th>Total</th></tr>
                            </thead>
                            <tbody>${ingredientsHTML}</tbody>
                        </table>
                    </td>
                </tr>`;

            const container = document.createElement('tbody');
            container.innerHTML = rowHTML;
            const row = container.firstElementChild;
            const detailRow = container.lastElementChild;

            row.addEventListener('click', (e) => {
                if (!e.target.matches('.ingredient-price, .market-price')) {
                    if (ForceCalc.crafting.currentlyOpenRow && ForceCalc.crafting.currentlyOpenRow !== detailRow) {
                        ForceCalc.crafting.currentlyOpenRow.style.display = 'none';
                    }
                    detailRow.style.display = detailRow.style.display === 'none' ? 'table-row' : 'none';
                    ForceCalc.crafting.currentlyOpenRow = (detailRow.style.display === 'table-row') ? detailRow : null;
                }
            });

            ForceCalc.utils.getElement(row, '.market-price')?.addEventListener('input', ForceCalc.crafting.handlePriceChange);
            detailRow.querySelectorAll('.ingredient-price').forEach(el => el.addEventListener('input', ForceCalc.crafting.handlePriceChange));
            row.querySelector('.favorite-btn').addEventListener('click', (e) =>
                ForceCalc.ui.toggleFavorite(e, item.Recipe)
            );

            // Add event listener for batch calculation button in the details row
            detailRow.querySelector('.batch-calc-btn').addEventListener('click', (e) => {
                e.stopPropagation(); // Prevent row click event
                ForceCalc.crafting.openBatchModal(item.Recipe);
            });
            return [row, detailRow];
        },

        // Create an ingredient row in the details section
        createIngredientRow: (item, i) => {
            const itemNameKey = `Item${i + 1}`, itemQuantityKey = `Item ${i + 1} Quantity`;
            if (!item[itemNameKey] || !item[itemQuantityKey]) return '';
            const itemName = item[itemNameKey];

            // Use our utility method or fall back to direct access
            let savedPrice = ForceCalc.utils.getMarketPrice(itemName);
            if (savedPrice <= 0) {
                // If not found as market price, check ingredient prices
                savedPrice = ForceCalc.data.ingredientPrices.ingredients[itemName] || 0;
            }

            const quantity = parseInt(item[itemQuantityKey] || 0);
            const calculatedPrice = savedPrice; // Show unit price directly

            // Add icon for Force Essence - we're not adding icons to ingredients as per user request
            let itemDisplay = itemName;

            return `
                <tr>
                    <td>${itemDisplay}</td>
                    <td>${item[itemQuantityKey]}</td>
                    <td><span contenteditable="true" class="ingredient-price" data-ingredient-name="${itemName}" data-quantity="${item[itemQuantityKey]}">${calculatedPrice}</span></td>
                    <td class="ingredient-total">0</td>
                </tr>`;
        },

        // Handle price changes in the table
        handlePriceChange: (e) => {
            const target = e.target;
            const newPrice = ForceCalc.utils.parseNum(target.textContent);

            if (target.classList.contains('ingredient-price')) {
                const ingredientName = target.dataset.ingredientName;
                // Store update in pending map instead of triggering immediate update
                ForceCalc.data.pendingPriceUpdates.set(ingredientName, {
                    type: 'ingredient',
                    price: newPrice
                });
            } else if (target.classList.contains('market-price')) {
                const mainRow = target.closest('.crafting-item-row') || target.closest('tr');
                const itemName = mainRow.dataset.itemName || target.dataset.itemName;
                const recipeName = mainRow.dataset.recipeName;

                if (recipeName) {
                    ForceCalc.data.pendingPriceUpdates.set(recipeName, {
                        type: 'recipe',
                        price: newPrice
                    });
                }

                if (itemName) {
                    ForceCalc.data.pendingPriceUpdates.set(itemName, {
                        type: 'item',
                        price: newPrice
                    });
                } else if (target.dataset.itemName) {
                    ForceCalc.data.pendingPriceUpdates.set(target.dataset.itemName, {
                        type: 'item',
                        price: newPrice
                    });
                }
            }

            // Trigger the debounced update
            ForceCalc.crafting.debouncedProcessUpdates();
        },

        // Process all pending price updates in a single batch
        processPendingUpdates: () => {
            if (ForceCalc.data.pendingPriceUpdates.size === 0) return;

            // First pass: Update all prices in the data structure
            const affectedItems = new Set();
            ForceCalc.data.pendingPriceUpdates.forEach((update, itemName) => {
                // Save the price in our data structure
                ForceCalc.utils.setMarketPrice(itemName, update.price);
                affectedItems.add(itemName);

                // Add normalized version to ensure we catch all related items
                const normalizedName = ForceCalc.utils.normalizeItemName(itemName);

                // Find all related items with the same normalized name
                // For recipes
                Object.keys(ForceCalc.crafting.recipesByItemName).forEach(key => {
                    if (ForceCalc.utils.normalizeItemName(key) === normalizedName) {
                        ForceCalc.crafting.recipesByItemName[key].forEach(recipe => {
                            ForceCalc.utils.setMarketPrice(recipe.Recipe, update.price);
                            affectedItems.add(recipe.Recipe);
                        });
                    }
                });

                // For ingredients
                Object.keys(ForceCalc.crafting.ingredientsByItemName).forEach(key => {
                    if (ForceCalc.utils.normalizeItemName(key) === normalizedName) {
                        // This item is used as an ingredient, update all recipes using it
                        ForceCalc.crafting.ingredientsByItemName[key].forEach(recipe => {
                            affectedItems.add(recipe.Recipe);
                        });
                    }
                });

                // Also check if any ingredients match this item name
                for (let i = 1; i <= 5; i++) {
                    document.querySelectorAll(`.ingredient-price[data-ingredient-name]`).forEach(el => {
                        const ingredientName = el.dataset.ingredientName;
                        if (ForceCalc.utils.normalizeItemName(ingredientName) === normalizedName) {
                            ForceCalc.utils.setTextPreserveCursor(el, update.price.toString());
                            const row = el.closest('tr');
                            if (row) {
                                const quantity = parseInt(el.dataset.quantity) || 0;
                                const totalCell = row.querySelector('.ingredient-total');
                                if (totalCell) {
                                    totalCell.textContent = ForceCalc.utils.formatWithDots((quantity * update.price).toFixed(0));
                                }

                                // Mark the parent recipe as affected
                                const recipeRow = el.closest('.recipe-details-row')?.previousElementSibling;
                                if (recipeRow && recipeRow.dataset.recipeName) {
                                    affectedItems.add(recipeRow.dataset.recipeName);
                                }
                            }
                        }
                    });
                }
            });

            // Second pass: Update UI for all affected items
            affectedItems.forEach(itemName => {
                const price = ForceCalc.utils.getMarketPrice(itemName);

                // Update market prices
                document.querySelectorAll(`.market-price[data-item-name="${itemName}"]`).forEach(el => {
                    ForceCalc.utils.setTextPreserveCursor(el, price.toString());
                });

                // Also check by the recipe name in data attributes
                document.querySelectorAll(`.crafting-item-row[data-recipe-name="${itemName}"] .market-price`).forEach(el => {
                    ForceCalc.utils.setTextPreserveCursor(el, price.toString());
                });
            });

            // Clear the pending updates map
            ForceCalc.data.pendingPriceUpdates.clear();

            // Then do a single pass to recalculate all affected rows
            document.querySelectorAll('.crafting-item-row').forEach(row => {
                if (affectedItems.has(row.dataset.recipeName) ||
                    affectedItems.has(row.dataset.itemName)) {
                    ForceCalc.crafting.recalculateRow(row);
                }
            });

            // Save prices once
            ForceCalc.storage.savePrices();

            // Filter table once
            ForceCalc.crafting.filterTable();
        },

        // Create a debounced version of the update processor
        debouncedProcessUpdates: null, // Will be initialized in init()

        // Update ingredient prices throughout the table
        updateIngredientPrices: (ingredientName, newPrice) => {
            if (!ingredientName) return;

            const baseIngredientName = ingredientName.replace(/\s*x\s*\d+$/, '');
            const normalizedName = ForceCalc.utils.normalizeItemName(baseIngredientName);

            // First update in ingredients map
            ForceCalc.data.ingredientPrices.ingredients[ingredientName] = newPrice;

            // Also update as market price for consistent cross-calculator price sharing
            ForceCalc.utils.setMarketPrice(ingredientName, newPrice);

            // Update all ingredient spans with the same normalized name
            document.querySelectorAll('.ingredient-price').forEach(priceSpan => {
                const spanIngredientName = priceSpan.dataset.ingredientName;
                const normalizedSpanName = ForceCalc.utils.normalizeItemName(spanIngredientName);

                if (normalizedSpanName === normalizedName) {
                    ForceCalc.utils.setTextPreserveCursor(priceSpan, newPrice.toString());

                    const row = priceSpan.closest('tr');
                    const quantity = parseInt(priceSpan.dataset.quantity) || 0;
                    const totalCell = row.querySelector('.ingredient-total');
                    ForceCalc.utils.setText(totalCell, ForceCalc.utils.formatWithDots((quantity * newPrice).toFixed(0)));

                    const detailRow = priceSpan.closest('.recipe-details-row');
                    if (detailRow) {
                        ForceCalc.crafting.recalculateRow(detailRow.previousElementSibling);
                    }
                }
            });

            const matchingRecipes = ForceCalc.crafting.recipesByItemName[baseIngredientName] || [];
            matchingRecipes.forEach(recipe => {
                const recipeRow = document.querySelector(`.crafting-item-row[data-recipe-name="${recipe.Recipe}"]`);
                if (recipeRow) {
                    const marketPriceSpan = recipeRow.querySelector('.market-price');
                    if (marketPriceSpan) {
                        ForceCalc.utils.setTextPreserveCursor(marketPriceSpan, newPrice.toString());
                        ForceCalc.data.ingredientPrices.marketPrices[recipe.Recipe] = newPrice;
                        ForceCalc.crafting.recalculateRow(recipeRow);
                    }
                }
            });

            const recipesUsingItem = ForceCalc.crafting.ingredientsByItemName[normalizedName] || [];
            recipesUsingItem.forEach(recipe => {
                const recipeRow = document.querySelector(`.crafting-item-row[data-recipe-name="${recipe.Recipe}"]`);
                if (recipeRow) {
                    ForceCalc.crafting.recalculateRow(recipeRow);
                }
            });

            ForceCalc.storage.savePrices();
            ForceCalc.crafting.filterTable();
        },

        // Update recipe market prices throughout the table
        updateRecipeMarketPrices: (itemName, newPrice) => {
            if (!itemName) return;

            const normalizedItemName = ForceCalc.utils.normalizeItemName(itemName);

            // First, use our utility method to update the price in shared storage
            ForceCalc.utils.setMarketPrice(itemName, newPrice);

            // Update in crafting table - recipes that produce this item
            const recipesToUpdate = ForceCalc.crafting.recipesByItemName[itemName] || [];
            if (recipesToUpdate.length === 0) {
                // Check all recipes for normalized name matches
                Object.keys(ForceCalc.crafting.recipesByItemName).forEach(key => {
                    if (ForceCalc.utils.normalizeItemName(key) === normalizedItemName) {
                        recipesToUpdate.push(...ForceCalc.crafting.recipesByItemName[key]);
                    }
                });
            }

            recipesToUpdate.forEach(recipe => {
                const row = document.querySelector(`.crafting-item-row[data-recipe-name="${recipe.Recipe}"]`);
                if (row) {
                    const marketPriceSpan = row.querySelector('.market-price');
                    if (marketPriceSpan) {
                        ForceCalc.utils.setTextPreserveCursor(marketPriceSpan, newPrice.toString());
                        ForceCalc.utils.setMarketPrice(recipe.Recipe, newPrice);
                        ForceCalc.crafting.recalculateRow(row);
                    }
                }
            });

            // Update in crafting table - as ingredients
            let recipesUsingItem = ForceCalc.crafting.ingredientsByItemName[normalizedItemName] || [];

            // If no direct match, check for normalized matches
            if (recipesUsingItem.length === 0) {
                Object.keys(ForceCalc.crafting.ingredientsByItemName).forEach(key => {
                    if (ForceCalc.utils.normalizeItemName(key) === normalizedItemName) {
                        recipesUsingItem = recipesUsingItem.concat(ForceCalc.crafting.ingredientsByItemName[key]);
                    }
                });
            }

            recipesUsingItem.forEach(recipe => {
                const recipeRow = document.querySelector(`.crafting-item-row[data-recipe-name="${recipe.Recipe}"]`);
                if (recipeRow) {
                    const detailRow = recipeRow.nextElementSibling;
                    const ingredientPriceSpans = detailRow.querySelectorAll('.ingredient-price');
                    ingredientPriceSpans.forEach(priceSpan => {
                        const normalizedSpanName = ForceCalc.utils.normalizeItemName(priceSpan.dataset.ingredientName);
                        if (normalizedSpanName === normalizedItemName) {
                            ForceCalc.utils.setTextPreserveCursor(priceSpan, newPrice.toString());
                            ForceCalc.data.ingredientPrices.ingredients[priceSpan.dataset.ingredientName] = newPrice;

                            const row = priceSpan.closest('tr');
                            const quantity = parseInt(priceSpan.dataset.quantity) || 0;
                            const totalCell = row.querySelector('.ingredient-total');
                            ForceCalc.utils.setText(totalCell, ForceCalc.utils.formatWithDots((quantity * newPrice).toFixed(0)));
                            ForceCalc.crafting.recalculateRow(recipeRow);
                        }
                    });
                }
            });

            // Also update all recipe rows where the item name matches
            document.querySelectorAll('.crafting-item-row').forEach(row => {
                const rowItemName = row.dataset.itemName;
                if (rowItemName && ForceCalc.utils.normalizeItemName(rowItemName) === normalizedItemName) {
                    const marketPriceSpan = row.querySelector('.market-price');
                    if (marketPriceSpan) {
                        ForceCalc.utils.setText(marketPriceSpan, newPrice.toString());
                        ForceCalc.utils.setMarketPrice(row.dataset.recipeName, newPrice);
                        ForceCalc.crafting.recalculateRow(row);
                    }
                }
            });

            ForceCalc.storage.savePrices();
            ForceCalc.crafting.filterTable();

            // If token shop calculator is on page, update its values too
            if (typeof ForceCalc.tokenShop?.updateItemPrices === 'function') {
                ForceCalc.tokenShop.updateItemPrices(itemName, newPrice);
            }
        },

        // Recalculate crafting metrics for a row
        recalculateRow: (row) => {
            const detailRow = row.nextElementSibling;
            const ingredientGrid = ForceCalc.utils.getElement(detailRow, '.ingredient-grid');
            const sellingPriceSpan = ForceCalc.utils.getElement(row, '.market-price');
            const successChance = ForceCalc.utils.parseNum(row.dataset.successChance);
            const craftAmount = ForceCalc.utils.parseNum(row.dataset.craftAmount);
            const { totalCraftCost, ingredientCosts } = ForceCalc.crafting.calculateTotalCost(ingredientGrid);

            // Format ingredient cost with dots
            ForceCalc.utils.setText(ForceCalc.utils.getElement(row, '.total-craft-cost'), ForceCalc.utils.formatWithDots(totalCraftCost.toFixed(0)));

            const sellingPrice = ForceCalc.utils.parseNum(sellingPriceSpan.textContent);
            const { profit, profitMargin } = ForceCalc.calculations.calculateCraftingMetrics(ingredientCosts, successChance, sellingPrice, craftAmount);

            // Check if any price is missing
            const missingRecipePrice = sellingPrice <= 0;
            const missingIngredientPrice = ingredientCosts.some(price => price <= 0);
            const missingAnyPrice = missingRecipePrice || missingIngredientPrice;

            // Calculate crafts to pay off the register cost
            const registerCost = ForceCalc.utils.parseNum(row.dataset.registerCost);
            const craftsToPayOff = ForceCalc.crafting.calculateCraftsToPayOff(registerCost, profit);

            // Update the crafts to pay off cell
            const craftsToPayOffCell = ForceCalc.utils.getElement(row, '.crafts-to-pay-off');
            if (craftsToPayOff > 0 && profit > 0) {
                ForceCalc.utils.setText(craftsToPayOffCell, Math.ceil(craftsToPayOff).toString());
            } else {
                ForceCalc.utils.setText(craftsToPayOffCell, registerCost > 0 ? '∞' : '-');
            }

            ForceCalc.crafting.updateTableRow(row, profit, profitMargin, missingAnyPrice);
        },

        // Calculate how many crafts it would take to pay off the register cost
        calculateCraftsToPayOff: (registerCost, profitPerCraft) => {
            if (registerCost <= 0 || profitPerCraft <= 0) {
                return 0;
            }
            return registerCost / profitPerCraft;
        },

        // Recalculate all rows in the table
        recalculateAllRows: () => {
            document.querySelectorAll('.crafting-item-row').forEach(ForceCalc.crafting.recalculateRow);
        },

        // Calculate total cost of ingredients
        calculateTotalCost: (ingredientGrid) => {
            let totalCost = 0;
            const ingredientCosts = [];
            ingredientGrid.querySelector('tbody').querySelectorAll('tr').forEach(gridRow => {
                const priceSpan = gridRow.querySelector('.ingredient-price');
                const quantityText = gridRow.cells[1].textContent;
                const quantity = ForceCalc.utils.parseNum(quantityText);
                const price = ForceCalc.utils.parseNum(priceSpan.textContent);
                const total = quantity * price;

                // Format the total cost of each ingredient with dots
                gridRow.querySelector('.ingredient-total').textContent = ForceCalc.utils.formatWithDots(total.toFixed(0));

                totalCost += total;
                ingredientCosts.push(total);
            });
            return { totalCraftCost: totalCost, ingredientCosts };
        },

        // Update table row with calculated profit values
        updateTableRow: (row, profit, profitMarginValue, missingAnyPrice) => {
            const profitMarginCell = ForceCalc.utils.getElement(row, '.profit-margin');
            const profitPerCraftCell = ForceCalc.utils.getElement(row, '.profit-per-craft');

            // Format profit margin
            ForceCalc.utils.setText(profitMarginCell, profitMarginValue.toFixed(2) + '%');

            // Remove all profit status classes from cells
            profitMarginCell.classList.remove('profit-negative', 'profit-low', 'profit-medium', 'profit-high');
            profitPerCraftCell.classList.remove('profit-negative', 'profit-low', 'profit-medium', 'profit-high');

            const profitClass = ForceCalc.utils.getProfitStatus(profitMarginValue);
            profitMarginCell.classList.add(profitClass);
            profitPerCraftCell.classList.add(profitClass);

            // Create profit text span with dot formatting
            const profitText = document.createElement('span');
            profitText.textContent = ForceCalc.utils.formatWithDots(profit.toFixed(0));

            // Clear existing content
            profitPerCraftCell.innerHTML = '';

            // Add profit text
            profitPerCraftCell.appendChild(profitText);

            // Add warning if needed
            if (missingAnyPrice) {
                const warning = document.createElement('span');
                warning.className = 'price-warning';
                warning.title = 'Missing ingredient or recipe price';
                warning.textContent = ' ⚠️';
                profitPerCraftCell.appendChild(warning);
            }
        },

        // Sort table by a specific column
        sortTable: (header) => {
            const table = header.closest('table');
            const tbody = table.querySelector('tbody');
            const columnIndex = Array.from(header.parentNode.children).indexOf(header);
            const currentDirection = header.classList.contains('sort-asc') ? 'desc' : 'asc';

            // Clear sorting indicators on all headers
            table.querySelectorAll('th').forEach(th => th.classList.remove('sort-asc', 'sort-desc'));

            // Set sorting indicator on current header
            header.classList.add(`sort-${currentDirection}`);

            const rows = Array.from(tbody.querySelectorAll('tr.crafting-item-row'));

            rows.sort((a, b) => {
                let aVal = a.children[columnIndex].textContent.trim();
                let bVal = b.children[columnIndex].textContent.trim();

                // Special handling for formatted numbers with dots as thousands separators
                if (aVal.includes('.') && !aVal.includes('%')) {
                    // Parse number with thousands separators
                    aVal = parseFloat(aVal.replace(/\./g, ''));
                } else if (aVal.includes('%')) {
                    // Handle percentage values
                    aVal = parseFloat(aVal.replace('%', '').trim());
                } else if (aVal === '∞') {
                    // Handle infinity symbol
                    aVal = Infinity;
                } else if (aVal === '-') {
                    // Handle dash for N/A values
                    aVal = -1;
                } else if (!isNaN(parseFloat(aVal))) {
                    // Handle regular numbers
                    aVal = parseFloat(aVal);
                }

                if (bVal.includes('.') && !bVal.includes('%')) {
                    // Parse number with thousands separators
                    bVal = parseFloat(bVal.replace(/\./g, ''));
                } else if (bVal.includes('%')) {
                    // Handle percentage values
                    bVal = parseFloat(bVal.replace('%', '').trim());
                } else if (bVal === '∞') {
                    // Handle infinity symbol
                    bVal = Infinity;
                } else if (bVal === '-') {
                    // Handle dash for N/A values
                    bVal = -1;
                } else if (!isNaN(parseFloat(bVal))) {
                    // Handle regular numbers
                    bVal = parseFloat(bVal);
                }

                // Sort in ascending or descending order
                if (currentDirection === 'asc') {
                    return aVal < bVal ? -1 : aVal > bVal ? 1 : 0;
                } else {
                    return aVal > bVal ? -1 : aVal < bVal ? 1 : 0;
                }
            });

            // Reorder rows in the DOM
            rows.forEach(row => {
                const detailRow = row.nextElementSibling;
                tbody.append(row, detailRow);
            });
        },


        // Add demand filter (hide items with price 0, show only favorites)
        addDemandFilter: () => {
            const filterContainer = document.createElement('div');
            filterContainer.id = 'demand-filter';
            filterContainer.innerHTML = `
                <label><input type="checkbox" id="price-filter"> Hide items with price 0</label>
                <label><input type="checkbox" id="favorites-filter"> ⭐ Show Only Favorites</label>
                <label><input type="checkbox" id="register-cost-filter"> Show Register Cost Info</label>
            `;
            filterContainer.querySelectorAll('input[type="checkbox"]').forEach(cb => cb.addEventListener('change', ForceCalc.crafting.filterTable));
            const placeholder = document.getElementById('crafting-calculator-placeholder');
            placeholder?.parentNode.insertBefore(filterContainer, placeholder);
            ForceCalc.storage.loadFilterSettings(); // Add this line
        },

        // Add category filter
        addCategoryFilter: () => {
            const filterContainer = document.createElement('div');
            filterContainer.id = 'category-filter';

            // Add title for the category section
            const titleDiv = document.createElement('div');
            titleDiv.className = 'filter-section-title';
            titleDiv.textContent = 'Categories';
            filterContainer.appendChild(titleDiv);

            let filterHTML = '';
            ForceCalc.crafting.recipeCategories.forEach(category => {
                filterHTML += `<label><input type="checkbox" value="${category}" checked> ${category}</label>`;
            });

            const filterContent = document.createElement('div');
            filterContent.className = 'filter-content';
            filterContent.innerHTML = filterHTML;
            filterContainer.appendChild(filterContent);

            const placeholder = document.getElementById('crafting-calculator-placeholder');
            placeholder?.parentNode.insertBefore(filterContainer, placeholder);

            filterContainer.querySelectorAll('input[type="checkbox"]').forEach(cb =>
                cb.addEventListener('change', ForceCalc.crafting.filterTable)
            );

            ForceCalc.storage.loadFilterSettings();
        },

        // Filter table based on selected filters
        filterTable: () => {
            const checkedCategories = Array.from(document.querySelectorAll('#category-filter input:checked'))
                .map(cb => cb.value);
            const hideZeroPrice = document.getElementById('price-filter')?.checked || false;
            const showOnlyFavorites = document.getElementById('favorites-filter')?.checked || false;
            const showRegisterCost = document.getElementById('register-cost-filter')?.checked || false;
            const searchQuery = document.getElementById('search-input')?.value.trim().toLowerCase() || '';

            // Toggle register cost columns visibility
            document.querySelectorAll('th.register-cost-column, td.register-cost-column').forEach(col => {
                col.style.display = showRegisterCost ? 'table-cell' : 'none';
            });

            document.querySelectorAll('.crafting-item-row').forEach(row => {
                const category = row.dataset.category || 'N/A';
                const detailRow = row.nextElementSibling;
                const showByCategory = checkedCategories.includes(category);

                let showByPrice = true;
                if (hideZeroPrice) {
                    const marketPrice = ForceCalc.utils.parseNum(ForceCalc.utils.getElement(row, '.market-price').textContent);
                    const ingredientPrices = Array.from(detailRow.querySelectorAll('.ingredient-price'))
                        .map(el => ForceCalc.utils.parseNum(el.textContent));
                    showByPrice = marketPrice > 0 && ingredientPrices.every(price => price > 0);
                }

                const recipeName = row.dataset.recipeName;
                const itemName = row.dataset.itemName;
                const showByFavorite = !showOnlyFavorites || ForceCalc.data.favoriteRecipes.has(recipeName);

                // Search functionality
                let showBySearch = true;
                if (searchQuery) {
                    // Normalize search query - remove brackets and extra spaces
                    const normalizedQuery = searchQuery.replace(/[\(\)\[\]]/g, ' ').replace(/\s+/g, ' ').trim();
                    // Split into individual search terms for more flexible matching
                    const searchTerms = normalizedQuery.split(' ');

                    // Normalize recipe name and item name
                    const normalizedRecipeName = recipeName.toLowerCase().replace(/[\(\)\[\]]/g, ' ').replace(/\s+/g, ' ').trim();
                    const normalizedItemName = itemName ? itemName.toLowerCase().replace(/[\(\)\[\]]/g, ' ').replace(/\s+/g, ' ').trim() : '';

                    // Check if all search terms are found in recipe name or item name
                    const recipeNameMatch = searchTerms.every(term => normalizedRecipeName.includes(term));
                    const itemNameMatch = normalizedItemName && searchTerms.every(term => normalizedItemName.includes(term));

                    // Check if any ingredient contains all search terms
                    const ingredientNames = Array.from(detailRow.querySelectorAll('.ingredient-price'))
                        .map(el => el.dataset.ingredientName.toLowerCase().replace(/[\(\)\[\]]/g, ' ').replace(/\s+/g, ' ').trim());

                    const ingredientMatch = ingredientNames.some(name =>
                        searchTerms.every(term => name.includes(term))
                    );

                    showBySearch = recipeNameMatch || itemNameMatch || ingredientMatch;
                }

                const show = showByCategory && showByPrice && showByFavorite && showBySearch;
                row.style.display = show ? '' : 'none';
                if (detailRow) {
                    detailRow.style.display = show && detailRow.style.display === 'table-row' ? 'table-row' : 'none';
                }
            });

            ForceCalc.storage.saveFilterSettings();
        },

        // Get stack size for an item based on its name
        getItemStackSize: (itemName) => {
            // Use the shared ItemData if available, otherwise use default
            if (typeof ItemData !== 'undefined' && ItemData.getStackSize) {
                return ItemData.getStackSize(itemName);
            }

            // Fallback to default stack size
            return 127;
        },

        // Open batch calculation modal for a specific recipe
        openBatchModal: (recipeName) => {
            const modal = document.getElementById('batch-modal-overlay');
            const modalHeader = modal.querySelector('.batch-modal-header h3');
            const resultsContainer = modal.querySelector('.batch-results');

            // Reset modal state
            resultsContainer.style.display = 'none';
            modal.querySelector('#batch-quantity').value = 10;
            modal.querySelector('#exclude-register-cost').checked = true;

            // Store the current recipe name in the modal's dataset
            modal.dataset.recipe = recipeName;

            // Update modal title with recipe name
            modalHeader.textContent = `Batch Calculation: ${recipeName}`;

            // Show the modal
            modal.classList.add('active');
        },

        // Calculate batch results
        calculateBatch: () => {
            const modal = document.getElementById('batch-modal-overlay');
            const recipeName = modal.dataset.recipe;
            const batchQuantity = parseInt(modal.querySelector('#batch-quantity').value) || 10;
            const excludeRegisterCost = modal.querySelector('#exclude-register-cost').checked;
            const resultsContainer = modal.querySelector('.batch-results');
            const materialsList = modal.querySelector('.batch-materials-list');
            const summaryContainer = modal.querySelector('.batch-summary');

            // Find the recipe row
            const recipeRow = document.querySelector(`.crafting-item-row[data-recipe-name="${recipeName}"]`);
            if (!recipeRow) return;

            // Get recipe details
            const detailRow = recipeRow.nextElementSibling;
            const ingredientGrid = detailRow.querySelector('.ingredient-grid');
            const sellingPriceSpan = recipeRow.querySelector('.market-price');
            const successChance = ForceCalc.utils.parseNum(recipeRow.dataset.successChance);
            const craftAmount = ForceCalc.utils.parseNum(recipeRow.dataset.craftAmount);
            const registerCost = ForceCalc.utils.parseNum(recipeRow.dataset.registerCost);

            // Calculate costs and profits for a single craft
            const { totalCraftCost, ingredientCosts } = ForceCalc.crafting.calculateTotalCost(ingredientGrid);
            const sellingPrice = ForceCalc.utils.parseNum(sellingPriceSpan.textContent);
            const { profit: profitPerCraft, profitMargin } = ForceCalc.calculations.calculateCraftingMetrics(
                ingredientCosts, successChance, sellingPrice, craftAmount
            );

            // Calculate batch metrics
            const totalCraftingCost = totalCraftCost * batchQuantity;
            const expectedSuccessfulCrafts = (successChance / 100) * batchQuantity;
            const expectedOutputQuantity = expectedSuccessfulCrafts * craftAmount;
            const expectedRevenue = expectedOutputQuantity * sellingPrice * (1 - ForceCalc.data.salesFee / 100);

            // Calculate profit with or without register cost
            const effectiveRegisterCost = excludeRegisterCost ? 0 : registerCost;
            const totalCost = totalCraftingCost + (excludeRegisterCost ? 0 : registerCost);
            const expectedProfit = expectedRevenue - totalCost;
            const batchProfitMargin = (expectedProfit / totalCraftingCost) * 100;

            // Get materials needed
            const materials = [];
            ingredientGrid.querySelector('tbody').querySelectorAll('tr').forEach(gridRow => {
                const itemName = gridRow.cells[0].textContent.trim();
                const quantityPerCraft = ForceCalc.utils.parseNum(gridRow.cells[1].textContent);
                const pricePerUnit = ForceCalc.utils.parseNum(gridRow.querySelector('.ingredient-price').textContent);
                const totalQuantity = quantityPerCraft * batchQuantity;
                const totalCost = totalQuantity * pricePerUnit;

                // Get the appropriate stack size for this item
                const stackSize = ForceCalc.crafting.getItemStackSize(itemName);

                // Only show stack info if the item is stackable (stack size > 1)
                let stackDisplay = '';
                if (stackSize > 1 && totalQuantity >= 1) {
                    const fullStacks = Math.floor(totalQuantity / stackSize);
                    const remainingItems = totalQuantity % stackSize;

                    if (fullStacks > 0) {
                        stackDisplay = `${fullStacks} stack${fullStacks > 1 ? 's' : ''}`;
                        if (remainingItems > 0) {
                            stackDisplay += ` + ${remainingItems}`;
                        }
                    }
                }

                materials.push({
                    name: itemName,
                    quantity: totalQuantity,
                    stackDisplay: stackDisplay,
                    cost: totalCost
                });
            });

            // Display materials list
            materialsList.innerHTML = '';
            materials.forEach(material => {
                const materialItem = document.createElement('div');
                materialItem.className = 'batch-material-item';
                materialItem.innerHTML = `
                    <div class="material-name">${material.name} × ${material.quantity}${material.stackDisplay ? ` (${material.stackDisplay})` : ''}</div>
                    <div class="material-cost">${ForceCalc.utils.formatWithDots(material.cost.toFixed(0))} ALZ</div>
                `;
                materialsList.appendChild(materialItem);
            });

            // Display summary
            const profitClass = ForceCalc.utils.getProfitStatus(batchProfitMargin);
            summaryContainer.innerHTML = `
                <div class="batch-summary-item">
                    <div class="label">Total Crafting Cost:</div>
                    <div class="value">${ForceCalc.utils.formatWithDots(totalCraftingCost.toFixed(0))} ALZ</div>
                </div>
                <div class="batch-summary-item">
                    <div class="label">Register Cost (one-time):</div>
                    <div class="value">${ForceCalc.utils.formatWithDots(registerCost.toFixed(0))} ALZ ${excludeRegisterCost ? '(excluded)' : '(included)'}</div>
                </div>
                <div class="batch-summary-item">
                    <div class="label">Expected Output:</div>
                    <div class="value">${expectedOutputQuantity.toFixed(1)} items (${successChance}% success)</div>
                </div>
                <div class="batch-summary-item">
                    <div class="label">Expected Revenue:</div>
                    <div class="value">${ForceCalc.utils.formatWithDots(expectedRevenue.toFixed(0))} ALZ</div>
                </div>
                <div class="batch-summary-item ${profitClass}">
                    <div class="label">Expected Profit:</div>
                    <div class="value">${ForceCalc.utils.formatWithDots(expectedProfit.toFixed(0))} ALZ</div>
                </div>
                <div class="batch-summary-item ${profitClass}">
                    <div class="label">Profit Margin:</div>
                    <div class="value">${batchProfitMargin.toFixed(2)}%</div>
                </div>
            `;

            // Show results
            resultsContainer.style.display = 'block';
        },

        // Initialize the Chloe calculator
        init: () => {
            // Initialize the debounced update processor
            ForceCalc.crafting.debouncedProcessUpdates = ForceCalc.utils.debounce(
                ForceCalc.crafting.processPendingUpdates,
                20 // 10ms debounce delay
            );

            // Use the ChloeData object from chloe-data.js instead of fetching from Google Sheets
            if (typeof ChloeData !== 'undefined' && ChloeData.items) {
                // Transform the data format to match the expected format from CSV
                ForceCalc.crafting.craftingData = ChloeData.items.map(item => {
                    const transformedItem = {
                        'Recipe': item.recipe,
                        'Item Name': item.name,
                        'Item Quantity': item.outputQuantity,
                        'Req Amity': item.requiredAmity,
                        'Success Rate': item.successRate,
                        'Register Cost Alz': item.registerCost,
                        'Category': item.category,
                        'iconPath': item.iconPath, // Preserve iconPath
                        'Tokens': item.tokens,
                        'Time': item.time,
                        'Demand': item.demand,
                        'Obtained Amity': item.obtainedAmity
                    };

                    // Map ingredients to item1, item1quantity format
                    if (item.ingredients && item.ingredients.length) {
                        item.ingredients.forEach((ingredient, idx) => {
                            transformedItem[`Item${idx + 1}`] = ingredient.name;
                            transformedItem[`Item ${idx + 1} Quantity`] = ingredient.quantity;
                        });
                    }

                    return transformedItem;
                });

                // Extract unique categories before creating table
                const uniqueCategories = new Set();
                ForceCalc.crafting.craftingData.forEach(item => {
                    const category = item.Category;
                    if (category) {
                        uniqueCategories.add(category);
                    }
                });
                ForceCalc.crafting.recipeCategories = Array.from(uniqueCategories).sort();

                ForceCalc.crafting.createTable(ForceCalc.crafting.craftingData);
            } else {
                // Fallback to CSV if ChloeData is not available
                const csvUrl = 'https://docs.google.com/spreadsheets/d/e/2PACX-1vSM01thoZ8a8t6er2AoxwF8UPjYurtuhYr7TqK5K4luq04C8rfNiA9nX-9ZjCZ0stpIvQTsWtTrqqVz/pub?gid=230388360&single=true&output=csv';
                fetch(csvUrl)
                    .then(response => response.ok ? response.text() : Promise.reject(`HTTP error! status: ${response.status}`))
                    .then(csvText => {
                        Papa.parse(csvText, {
                            header: true,
                            dynamicTyping: true,
                            skipEmptyLines: true,
                            complete: (results) => {
                                ForceCalc.crafting.craftingData = results.data.filter(item => item.Recipe && item.Recipe.trim());

                                // Extract unique categories before creating table
                                const uniqueCategories = new Set();
                                ForceCalc.crafting.craftingData.forEach(item => {
                                    const category = item.Category;
                                    if (category) {
                                        uniqueCategories.add(category);
                                    }
                                });
                                ForceCalc.crafting.recipeCategories = Array.from(uniqueCategories).sort();

                                ForceCalc.crafting.createTable(ForceCalc.crafting.craftingData);
                            }
                        });
                    })
                    .catch(error => {
                        console.error("Error loading crafting data:", error);
                        const placeholder = document.getElementById('crafting-calculator-placeholder');
                        if (placeholder) {
                            placeholder.innerHTML = '<p class="error-message">Failed to load crafting data. Please try refreshing the page.</p>';
                        }
                    });
            }
        }
    };

    // Initialize the Chloe calculator
    ForceCalc.crafting.init();
});